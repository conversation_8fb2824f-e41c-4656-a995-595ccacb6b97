import { cn } from '@/app/lib/utils'
import * as React from 'react'

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<'input'>>(
    ({ className, type, onFocus, ...props }, ref) => {
        const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
            // Auto-clear "0" value for number inputs when focused
            if (type === 'number' && e.target.value === '0') {
                e.target.value = ''
            }
            // Call the original onFocus handler if provided
            onFocus?.(e)
        }

        return (
            <input
                type={type}
                className={cn(
                    'flex h-11 w-full text-input text-base leading-5 rounded-md border border-border bg-card px-3 py-1 transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-background0 placeholder:text-neutral-400 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
                    className,
                )}
                ref={ref}
                onFocus={handleFocus}
                {...props}
            />
        )
    },
)
Input.displayName = 'Input'

export { Input }
