"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/layout",{

/***/ "(app-pages-browser)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: function() { return /* binding */ Input; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(_c = (param, ref)=>{\n    let { className, type, onFocus, ...props } = param;\n    const handleFocus = (e)=>{\n        // Auto-clear \"0\" value for number inputs when focused\n        if (type === \"number\" && e.target.value === \"0\") {\n            e.target.value = \"\";\n        }\n        // Call the original onFocus handler if provided\n        onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex h-11 w-full text-input text-base leading-5 rounded-md border border-border bg-card px-3 py-1 transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-background0 placeholder:text-neutral-400 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        onFocus: handleFocus,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 16,\n        columnNumber: 13\n    }, undefined);\n});\n_c1 = Input;\nInput.displayName = \"Input\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Input$React.forwardRef\");\n$RefreshReg$(_c1, \"Input\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/input.tsx\n"));

/***/ })

});