"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/logbook/trip-log.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TripLog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/client */ \"(app-pages-browser)/./src/app/offline/models/client.js\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/eventType */ \"(app-pages-browser)/./src/app/offline/models/eventType.js\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _depart_time__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./depart-time */ \"(app-pages-browser)/./src/app/ui/logbook/depart-time.tsx\");\n/* harmony import */ var _exp_arrival_time__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./exp-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/exp-arrival-time.tsx\");\n/* harmony import */ var _actual_arrival_time__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./actual-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx\");\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./events */ \"(app-pages-browser)/./src/app/ui/logbook/events.tsx\");\n/* harmony import */ var _trip_log_pob__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./trip-log-pob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-pob.tsx\");\n/* harmony import */ var _trip_log_vob__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./trip-log-vob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-vob.tsx\");\n/* harmony import */ var _trip_log_dgr__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./trip-log-dgr */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-dgr.tsx\");\n/* harmony import */ var _components_trip_comments__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! ./components/trip-comments */ \"(app-pages-browser)/./src/app/ui/logbook/components/trip-comments.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_master__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! ./components/master */ \"(app-pages-browser)/./src/app/ui/logbook/components/master.tsx\");\n/* harmony import */ var _components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! @/components/ui/table-wrapper */ \"(app-pages-browser)/./src/components/ui/table-wrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React and Next.js imports\n\n\n\n\n\n\n// Utility imports\n\n\n\n\n\n\n// Model imports\n\n\n\n\n\n// UI Component imports\n\n\n\n\n\n\n\n\n\n\n// Custom component imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TripLog(param) {\n    let { tripReport = false, logBookConfig, updateTripReport, locked, crewMembers, masterID, createdTab = false, setCreatedTab, currentTrip = false, setCurrentTrip, vessels, offline = false, fuelLogs, logBookStartDate } = param;\n    var _tripReport_find;\n    _s();\n    // const router = useRouter()\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast)();\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_40__.useQueryState)(\"tab\", {\n        defaultValue: \"crew\"\n    });\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [eventTypes, setEventTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openTripModal, setOpenTripModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bufferTripID, setBufferTripID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedDGR, setSelectedDGR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [openRiskAnalysis, setOpenTripStartRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoods, setDisplayDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [openEventModal, setOpenEventModal] = useState(false)\n    // const [selectedRowdgr, setSelectedRowdgr] = useState<any>(false)\n    const [tripReport_Stops, setTripReport_Stops] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDGRPVPD, setSelectedDGRPVPD] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [allPVPDDangerousGoods, setAllPVPDDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRowEvent, setSelectedRowEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [riskBufferEvDgr, setRiskBufferEvDgr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDangerousGoods, setAllDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentEventTypeEvent, setCurrentEventTypeEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStopEvent, setCurrentStopEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signatureKey, setSignatureKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n    const currentTripRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canCarryDangerousGoods = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesDangerousGoods);\n    }, [\n        vessel\n    ]);\n    const canCarryVehicles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesVehicles);\n    }, [\n        vessel\n    ]);\n    // Initialize client\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getOneClient)(setClient);\n    }\n    // Update signature state when currentTrip changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentTrip && currentTrip.sectionSignature) {\n            setSignature(currentTrip.sectionSignature.signatureData || \"\");\n        } else {\n            setSignature(\"\");\n        }\n    }, [\n        currentTrip\n    ]);\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(tripReport ? tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.comment : \"\");\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_tripReport, setEdit_tripReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const clientModel = new _app_offline_models_client__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const eventTypeModel = new _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const [openTripSelectionDialog, setOpenTripSelectionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tripReportSchedules, setTripReportSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripReportSchedule, setSelectedTripReportSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tripScheduleServices, setTripScheduleServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripScheduleServiceID, setSelectedTripScheduleServiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showNextTrips, setShowNextTrips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_LOGBOOKENTRY_TRIPREPORT\", permissions)) {\n                setEdit_tripReport(true);\n            } else {\n                setEdit_tripReport(false);\n            }\n        }\n    };\n    const offlineLoad = async ()=>{\n        const locations = await geoLocationModel.getAll();\n        setLocations(locations);\n        const types = await eventTypeModel.getAll();\n        setEventTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        if (!locations) {\n            if (offline) {\n                offlineLoad();\n            } else {\n                loadLocations();\n                loadEventTypes();\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (createdTab) {\n            setSelectedTab(createdTab);\n        }\n    }, [\n        createdTab\n    ]);\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getVesselByID)(+vesselID, setVessel);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tripReport && currentTrip) {\n            const trip = tripReport.find((trip)=>trip.id === currentTrip.id);\n            currentTripRef.current = trip;\n            setCurrentTrip(trip);\n        }\n        if (tripReport && bufferTripID > 0) {\n            const trip = tripReport.find((trip)=>trip.id === bufferTripID);\n            if (trip) {\n                currentTripRef.current = trip;\n                setCurrentTrip(trip);\n                // Automatically expand the accordion for the newly created trip\n                setAccordionValue(trip.id.toString());\n                setOpenTripModal(true);\n                setSelectedTab(trip.id);\n                setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n                // Initialize signature data if available\n                if (trip.sectionSignature) {\n                    setSignature(trip.sectionSignature.signatureData || \"\");\n                } else {\n                    setSignature(\"\");\n                }\n                // Initialize trip-specific state\n                setRiskBufferEvDgr(trip === null || trip === void 0 ? void 0 : trip.dangerousGoodsChecklist);\n                setOpenTripStartRiskAnalysis(false);\n                setAllDangerousGoods(false);\n                setCurrentStopEvent(false);\n                setCurrentEventTypeEvent(false);\n                setSelectedRowEvent(false);\n                setDisplayDangerousGoods((trip === null || trip === void 0 ? void 0 : trip.enableDGR) === true);\n                setDisplayDangerousGoodsSailing((trip === null || trip === void 0 ? void 0 : trip.designatedDangerousGoodsSailing) === true);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setAllPVPDDangerousGoods(false);\n                setSelectedDGRPVPD(false);\n                setTripReport_Stops(false);\n            }\n            setBufferTripID(0);\n        }\n    }, [\n        tripReport\n    ]);\n    const [loadLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setLocations(response.readGeoLocations.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading locations\", error);\n        }\n    });\n    const [loadEventTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_EVENT_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setEventTypes(response.readEventTypes.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading activity types\", error);\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_Stop, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const handleCreateTripReportScheduleStops = async (logBookEntrySectionID)=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(selectedTripReportSchedule)) {\n            const tripStops = selectedTripReportSchedule.tripReportScheduleStops.nodes || [];\n            await Promise.all(tripStops.map(async (stop)=>{\n                const input = {\n                    logBookEntrySectionID: logBookEntrySectionID,\n                    tripReportScheduleStopID: stop.id,\n                    arriveTime: stop.arriveTime,\n                    departTime: stop.departTime,\n                    stopLocationID: stop.stopLocationID\n                };\n                await createTripReport_Stop({\n                    variables: {\n                        input: input\n                    }\n                });\n            }));\n            setSelectedTripReportSchedule(null);\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        logBookEntrySectionID\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        logBookEntrySectionID\n                    ]\n                });\n            }\n        }\n    };\n    const [createTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            handleCreateTripReportScheduleStops(data.id);\n        /* if (!isEmpty(selectedTripReportSchedule)) {\r\n                    const tripStops =\r\n                        selectedTripReportSchedule.tripReportScheduleStops\r\n                            .nodes || []\r\n                    Promise.all(\r\n                        tripStops.map(async (stop: any) => {\r\n                            const input = {\r\n                                logBookEntrySectionID: data.id,\r\n                                tripReportScheduleStopID: stop.id,\r\n                                arriveTime: stop.arriveTime,\r\n                                departTime: stop.departTime,\r\n                                stopLocationID: stop.stopLocationID,\r\n                            }\r\n                            await createTripReport_Stop({\r\n                                variables: { input: input },\r\n                            })\r\n                        }),\r\n                    )\r\n                } */ },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n            setCurrentTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [readTripReportSchedules, { loading: readTripReportSchedulesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripReportSchedules, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripReportSchedules.nodes.filter((trip)=>// only show trips for the current vessel\n                trip.vehicles.nodes.some((vehicle)=>+vehicle.id === +vesselID));\n            if (showNextTrips) {\n                // only show 1 past trip and 4 upcoming trips\n                const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n                const pastIndex = data.findIndex((trip)=>trip.departTime >= currentTime);\n                const result = (pastIndex > 0 ? [\n                    data[pastIndex - 1]\n                ] : []).concat(data.slice(pastIndex, pastIndex + 4));\n                setTripReportSchedules(result);\n            } else {\n                setTripReportSchedules(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripReportSchedules\", error);\n        }\n    });\n    const [readTripScheduleServices] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripScheduleServices, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripScheduleServices.nodes.map((tss)=>{\n                return {\n                    label: tss.title,\n                    value: tss.id\n                };\n            });\n            setTripScheduleServices(data);\n            setTripReportSchedules([]);\n            setOpenTripSelectionDialog(true);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripScheduleServices\", error);\n        }\n    });\n    const loadTripScheduleServices = async ()=>{\n        await readTripScheduleServices({\n            variables: {\n                filter: {\n                    vehicles: {\n                        id: {\n                            eq: vesselID\n                        }\n                    }\n                }\n            }\n        });\n    };\n    const loadTripReportSchedules = async (tripScheduleServiceID)=>{\n        setTripReportSchedules([]);\n        await readTripReportSchedules({\n            variables: {\n                filter: {\n                    // archived: { eq: false },\n                    // start: { eq: logBookStartDate },\n                    tripScheduleServiceID: {\n                        eq: tripScheduleServiceID\n                    }\n                }\n            }\n        });\n    };\n    const doCreateTripReport = async (input)=>{\n        if (!edit_tripReport) {\n            toast({\n                title: \"Error\",\n                description: \"You do not have permission to add a trip\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        if (offline) {\n            const data = await tripReportModel.save({\n                ...input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)()\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            // For offline mode, immediately set accordion value since the trip is created synchronously\n            setAccordionValue(data.id.toString());\n        } else {\n            createTripReport_LogBookEntrySection({\n                variables: {\n                    input: input\n                }\n            });\n        }\n        setRiskBufferEvDgr(false);\n        setOpenTripStartRiskAnalysis(false);\n        setAllDangerousGoods(false);\n        setCurrentStopEvent(false);\n        setCurrentEventTypeEvent(false);\n        setSelectedRowEvent(false);\n        setDisplayDangerousGoods(false);\n        setDisplayDangerousGoodsSailing(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        setAllPVPDDangerousGoods(false);\n        setSelectedDGRPVPD(false);\n        setTripReport_Stops(false);\n    };\n    const handleAddTrip = async ()=>{\n        const allowedVesselTypes = [\n            \"SLALL\",\n            \"Tug_Boat\",\n            \"Passenger_Ferry\",\n            \"Water_Taxi\"\n        ];\n        if (allowedVesselTypes.includes(vessel.vesselType)) {\n            loadTripScheduleServices();\n        } else {\n            handleCustomTrip();\n        }\n    };\n    const handleSelectTripReportSchedule = (trip)=>{\n        setSelectedTripReportSchedule(trip);\n        setOpenTripSelectionDialog(false);\n        const input = {\n            tripReportScheduleID: trip.id,\n            departTime: trip.departTime,\n            arriveTime: trip.arriveTime,\n            fromLocationID: trip.fromLocationID,\n            toLocationID: trip.toLocationID,\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input);\n    };\n    const handleCustomTrip = ()=>{\n        setOpenTripSelectionDialog(false);\n        const input = {\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input);\n    };\n    // Removed unused handleEditTrip function\n    const [createLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            const data = response.createLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: +data.logBookEntrySectionID,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error saving signature\", error);\n        }\n    });\n    const [updateLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            var _currentTripRef_current;\n            const data = response.updateLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"341 TripLog updateLogBookEntrySection_Signature\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        // Log signature information for debugging\n        // Ensure we have a valid signature\n        const sigVariables = {\n            logBookEntrySectionID: currentTrip.id,\n            memberID: localStorage.getItem(\"userId\"),\n            signatureData: signature || \"\"\n        };\n        if (+currentTrip.sectionSignatureID > 0) {\n            // Update signature\n            updateLogBookEntrySection_Signature({\n                variables: {\n                    input: {\n                        ...sigVariables,\n                        id: +currentTrip.sectionSignatureID\n                    }\n                }\n            });\n        } else {\n            // Create signature\n            createLogBookEntrySection_Signature({\n                variables: {\n                    input: sigVariables !== null && sigVariables !== void 0 ? sigVariables : \"\"\n                }\n            });\n        }\n        if (offline) {\n            // updateTripReport_LogBookEntrySection\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                comment: comment || null\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n        } else {\n            var _currentTripRef_current;\n            await updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        comment: comment || null\n                    }\n                }\n            });\n            setOpenTripModal(false);\n            setCurrentTrip(false);\n        }\n    };\n    const displayFieldTripLog = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _dailyChecks__customisedComponentFields, _dailyChecks_;\n        const dailyChecks = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"TripReport_LogBookComponent\");\n        if ((dailyChecks === null || dailyChecks === void 0 ? void 0 : dailyChecks.length) > 0 && ((_dailyChecks_ = dailyChecks[0]) === null || _dailyChecks_ === void 0 ? void 0 : (_dailyChecks__customisedComponentFields = _dailyChecks_.customisedComponentFields) === null || _dailyChecks__customisedComponentFields === void 0 ? void 0 : _dailyChecks__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const displayField = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _dailyChecks__customisedComponentFields, _dailyChecks_;\n        const dailyChecks = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        if ((dailyChecks === null || dailyChecks === void 0 ? void 0 : dailyChecks.length) > 0 && ((_dailyChecks_ = dailyChecks[0]) === null || _dailyChecks_ === void 0 ? void 0 : (_dailyChecks__customisedComponentFields = _dailyChecks_.customisedComponentFields) === null || _dailyChecks__customisedComponentFields === void 0 ? void 0 : _dailyChecks__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const convertTimeFormat = (time)=>{\n        if (time === null || time === undefined) return \"\";\n        const [hours, minutes] = time.split(\":\");\n        return \"\".concat(hours, \":\").concat(minutes);\n    };\n    const handleCancel = ()=>{\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setAccordionValue(\"\");\n    };\n    // Removed unused functions\n    const initOffline = async ()=>{\n        var _localStorage_getItem;\n        // getOneClient\n        const client = await clientModel.getById((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0);\n        setClient(client);\n        // getVesselByID(+vesselID, setVessel)\n        const vessel = await vesselModel.getById(vesselID);\n        setVessel(vessel);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            initOffline();\n        }\n    }, [\n        offline\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.P, {\n                children: \"This section covers the logbook entry. This can be made up of a single trip or many over the course of the voyage.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 759,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: tripReport ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.Accordion, {\n                    type: \"single\",\n                    collapsible: true,\n                    value: accordionValue,\n                    onValueChange: (value)=>{\n                        setAccordionValue(value);\n                        // If we're closing the accordion, reset the state\n                        if (value === \"\") {\n                            setSelectedTab(0);\n                            setOpenTripModal(false);\n                            setCurrentTrip(false);\n                        } else {\n                            // If opening an accordion item, set the trip data\n                            const selectedTrip = tripReport.find((trip)=>trip.id.toString() === value);\n                            if (selectedTrip) {\n                                setSelectedTab(selectedTrip.id);\n                                setOpenTripModal(true);\n                                setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n                                currentTripRef.current = selectedTrip;\n                                setCurrentTrip(selectedTrip);\n                                // Initialize signature data if available\n                                if (selectedTrip.sectionSignature) {\n                                    setSignature(selectedTrip.sectionSignature.signatureData || \"\");\n                                } else {\n                                    setSignature(\"\");\n                                }\n                                setRiskBufferEvDgr(selectedTrip === null || selectedTrip === void 0 ? void 0 : selectedTrip.dangerousGoodsChecklist);\n                                setOpenTripStartRiskAnalysis(false);\n                                setAllDangerousGoods(false);\n                                setCurrentStopEvent(false);\n                                setCurrentEventTypeEvent(false);\n                                setSelectedRowEvent(false);\n                                setDisplayDangerousGoods((selectedTrip === null || selectedTrip === void 0 ? void 0 : selectedTrip.enableDGR) === true);\n                                setDisplayDangerousGoodsSailing((selectedTrip === null || selectedTrip === void 0 ? void 0 : selectedTrip.designatedDangerousGoodsSailing) === true);\n                                setDisplayDangerousGoodsPvpd(false);\n                                setDisplayDangerousGoodsPvpdSailing(null);\n                                setAllPVPDDangerousGoods(false);\n                                setSelectedDGRPVPD(false);\n                                setTripReport_Stops(false);\n                            }\n                        }\n                    },\n                    children: tripReport.filter((trip)=>!(trip === null || trip === void 0 ? void 0 : trip.archived)).map((trip, index)=>{\n                        var _trip_fromLocation, _trip_fromLocation1, _trip_toLocation, _trip_toLocation1, _trip_fromLocation2, _trip_toLocation2, _currentTripRef_current;\n                        // Generate trip display text\n                        const tripDisplayText = \"\".concat((trip === null || trip === void 0 ? void 0 : trip.departTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.departTime) + \" - \" : \"No depart time - \").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation = trip.fromLocation) === null || _trip_fromLocation === void 0 ? void 0 : _trip_fromLocation.title) || \"\").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation1 = trip.fromLocation) === null || _trip_fromLocation1 === void 0 ? void 0 : _trip_fromLocation1.title) && (trip === null || trip === void 0 ? void 0 : (_trip_toLocation = trip.toLocation) === null || _trip_toLocation === void 0 ? void 0 : _trip_toLocation.title) ? \" -> \" : \"\").concat((trip === null || trip === void 0 ? void 0 : trip.arrive) ? convertTimeFormat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(trip === null || trip === void 0 ? void 0 : trip.arrive).format(\"HH:mm \")) : (trip === null || trip === void 0 ? void 0 : trip.arriveTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.arriveTime) + \" - \" : \"- No arrival time \").concat((trip === null || trip === void 0 ? void 0 : (_trip_toLocation1 = trip.toLocation) === null || _trip_toLocation1 === void 0 ? void 0 : _trip_toLocation1.title) || \"\").concat(!(trip === null || trip === void 0 ? void 0 : (_trip_fromLocation2 = trip.fromLocation) === null || _trip_fromLocation2 === void 0 ? void 0 : _trip_fromLocation2.title) && !(trip === null || trip === void 0 ? void 0 : (_trip_toLocation2 = trip.toLocation) === null || _trip_toLocation2 === void 0 ? void 0 : _trip_toLocation2.title) ? \" - \" : \" \");\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionItem, {\n                            value: trip.id.toString(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tripDisplayText\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 45\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 859,\n                                    columnNumber: 41\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_23__.AccordionContent, {\n                                    className: \"px-5 sm:px-10\",\n                                    children: currentTrip && currentTrip.id === trip.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_36__.cn)(\"space-y-6\", locked || !edit_tripReport ? \"opacity-70 pointer-events-none\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-8 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col inset-y-0 items-center pt-10 pb-4 absolute -left-5 sm:-left-[25px] min-h-24 w-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_36__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 949,\n                                                                columnNumber: 65\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_36__.cn)(\"border-l h-full border-wedgewood-200 border-dashed\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 65\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_36__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 960,\n                                                                columnNumber: 65\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_depart_time__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        offline: offline,\n                                                        currentTrip: currentTrip,\n                                                        tripReport: tripReport,\n                                                        templateStyle: \"\",\n                                                        updateTripReport: updateTripReport\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_22__.Label, {\n                                                        label: \"Departure location\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            offline: offline,\n                                                            setCurrentLocation: (location)=>{\n                                                            // Store coordinates if needed for direct coordinate input\n                                                            },\n                                                            handleLocationChange: (selectedLocation)=>{\n                                                                // Update the from location\n                                                                if (offline) {\n                                                                    updateTripReport({\n                                                                        id: [\n                                                                            ...tripReport.map((trip)=>trip.id),\n                                                                            currentTrip.id\n                                                                        ],\n                                                                        currentTripID: currentTrip.id,\n                                                                        key: \"fromLocationID\",\n                                                                        value: selectedLocation.value,\n                                                                        label: selectedLocation.label\n                                                                    });\n                                                                } else {\n                                                                    var _currentTripRef_current;\n                                                                    // For online mode, use the mutation\n                                                                    updateTripReport_LogBookEntrySection({\n                                                                        variables: {\n                                                                            input: {\n                                                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                                                fromLocationID: selectedLocation.value\n                                                                            }\n                                                                        }\n                                                                    });\n                                                                }\n                                                            },\n                                                            currentEvent: {\n                                                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLocationID) || 0,\n                                                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLat) || 0,\n                                                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLong) || 0\n                                                            },\n                                                            showAddNewLocation: true,\n                                                            showUseCoordinates: true,\n                                                            showCurrentLocation: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                            lineNumber: 985,\n                                                            columnNumber: 65\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1061,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.H5, {\n                                                        children: \"PEOPLE ON BOARD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1062,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_pob__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                        offline: offline,\n                                                        currentTrip: currentTrip,\n                                                        tripReport: tripReport,\n                                                        vessel: vessel,\n                                                        crewMembers: crewMembers,\n                                                        logBookConfig: logBookConfig,\n                                                        masterTerm: client === null || client === void 0 ? void 0 : client.masterTerm,\n                                                        updateTripReport: updateTripReport\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1065,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1089,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: [\n                                                            canCarryVehicles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_vob__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                offline: offline,\n                                                                currentTrip: currentTrip,\n                                                                logBookConfig: logBookConfig\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1102,\n                                                                columnNumber: 69\n                                                            }, this),\n                                                            canCarryDangerousGoods && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_dgr__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                offline: offline,\n                                                                locked: locked || !edit_tripReport,\n                                                                currentTrip: currentTrip,\n                                                                logBookConfig: logBookConfig,\n                                                                selectedDGR: selectedDGR,\n                                                                members: crewMembers,\n                                                                displayDangerousGoods: displayDangerousGoods,\n                                                                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                                                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                                                allDangerousGoods: allDangerousGoods,\n                                                                setAllDangerousGoods: setAllDangerousGoods\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1117,\n                                                                columnNumber: 69\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                                lineNumber: 1157,\n                                                                columnNumber: 65\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_events__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        offline: offline,\n                                                        logBookStartDate: logBookStartDate,\n                                                        currentTrip: currentTrip,\n                                                        logBookConfig: logBookConfig,\n                                                        updateTripReport: updateTripReport,\n                                                        locked: locked,\n                                                        geoLocations: locations,\n                                                        tripReport: tripReport,\n                                                        crewMembers: crewMembers,\n                                                        masterID: masterID,\n                                                        vessel: vessel,\n                                                        vessels: vessels,\n                                                        setSelectedRow: setSelectedRowEvent,\n                                                        setCurrentEventType: setCurrentEventTypeEvent,\n                                                        setCurrentStop: setCurrentStopEvent,\n                                                        currentEventType: currentEventTypeEvent,\n                                                        currentStop: currentStopEvent,\n                                                        tripReport_Stops: tripReport_Stops,\n                                                        setTripReport_Stops: setTripReport_Stops,\n                                                        displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                                                        setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                                                        displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                                                        setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                                                        allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                                        setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                                        selectedDGRPVPD: selectedDGRPVPD,\n                                                        setSelectedDGRPVPD: setSelectedDGRPVPD,\n                                                        fuelLogs: fuelLogs\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1161,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_21__.Separator, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1243,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_22__.Label, {\n                                                        label: \"Arrival location\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            offline: offline,\n                                                            setCurrentLocation: (location)=>{\n                                                            // Store coordinates if needed for direct coordinate input\n                                                            },\n                                                            handleLocationChange: (selectedLoc)=>{\n                                                                // Update the to location\n                                                                if (offline) {\n                                                                    updateTripReport({\n                                                                        id: [\n                                                                            ...tripReport.map((trip)=>trip.id),\n                                                                            currentTrip.id\n                                                                        ],\n                                                                        currentTripID: currentTrip.id,\n                                                                        key: \"toLocationID\",\n                                                                        value: selectedLoc.value,\n                                                                        label: selectedLoc.label\n                                                                    });\n                                                                } else {\n                                                                    var _currentTripRef_current;\n                                                                    // For online mode, use the mutation\n                                                                    updateTripReport_LogBookEntrySection({\n                                                                        variables: {\n                                                                            input: {\n                                                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                                                toLocationID: selectedLoc.value\n                                                                            }\n                                                                        }\n                                                                    });\n                                                                }\n                                                            },\n                                                            currentEvent: {\n                                                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLocationID) || 0,\n                                                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLat) || 0,\n                                                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLong) || 0\n                                                            },\n                                                            showAddNewLocation: true,\n                                                            showUseCoordinates: true,\n                                                            showCurrentLocation: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                            lineNumber: 1245,\n                                                            columnNumber: 65\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1244,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 947,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_exp_arrival_time__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                offline: offline,\n                                                currentTrip: currentTrip,\n                                                tripReport: tripReport,\n                                                updateTripReport: updateTripReport\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1323,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_actual_arrival_time__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                offline: offline,\n                                                currentTrip: currentTrip,\n                                                tripReport: tripReport,\n                                                updateTripReport: updateTripReport\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1336,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trip_comments__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                offline: offline,\n                                                currentTrip: currentTrip,\n                                                tripReport: tripReport,\n                                                setCommentField: setComment,\n                                                updateTripReport: updateTripReport\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 57\n                                            }, this),\n                                            displayFieldTripLog(\"MasterID\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(locked || !edit_tripReport ? \"pointer-events-none\" : \"\", \" max-w-sm\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_master__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    tripReport: tripReport,\n                                                    crewMembers: crewMembers,\n                                                    updateTripReport: updateTripReport\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                    lineNumber: 1369,\n                                                    columnNumber: 65\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1367,\n                                                columnNumber: 61\n                                            }, this),\n                                            displayFieldTripLog(\"Signature\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                locked: locked,\n                                                title: \"Signature   Confirmation\",\n                                                description: \"By signing below, I   confirm that the   recorded entries are   accurate to the best   of my knowledge and   in accordance with   the vessel's   operating procedures   and regulations.\",\n                                                signature: currentTripRef === null || currentTripRef === void 0 ? void 0 : (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.sectionSignature,\n                                                onSignatureChanged: (sign)=>{\n                                                    setSignature(sign);\n                                                }\n                                            }, \"\".concat(signatureKey, \"-\").concat(trip.id), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1392,\n                                                columnNumber: 61\n                                            }, this),\n                                            !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_37__.FormFooter, {\n                                                className: \"justify-end gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                                        variant: \"back\",\n                                                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"],\n                                                        onClick: handleCancel,\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1423,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_44__[\"default\"],\n                                                        onClick: handleSave,\n                                                        children: \"Update\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1433,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1422,\n                                                columnNumber: 61\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 939,\n                                        columnNumber: 53\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 41\n                                }, this)\n                            ]\n                        }, \"triplog-\".concat(index), true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 856,\n                            columnNumber: 37\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 766,\n                    columnNumber: 21\n                }, this) : \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 764,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                    type: \"button\",\n                    onClick: handleAddTrip,\n                    disabled: locked,\n                    children: \"Add Trip\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1457,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1456,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_24__.AlertDialogNew, {\n                openDialog: openTripSelectionDialog,\n                setOpenDialog: setOpenTripSelectionDialog,\n                title: \"Select Trip\",\n                description: \"Select a trip from the list below\",\n                cancelText: \"Close\",\n                size: \"xl\",\n                handleCancel: ()=>setOpenTripSelectionDialog(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                onClick: handleCustomTrip,\n                                variant: \"primary\",\n                                children: \"Create Custom Trip\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 1471,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1470,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                            options: tripScheduleServices,\n                            value: tripScheduleServices.find((option)=>option.value === selectedTripScheduleServiceID) || null,\n                            onChange: (e)=>{\n                                if (e) {\n                                    setSelectedTripScheduleServiceID(e.value);\n                                    loadTripReportSchedules(e.value);\n                                } else {\n                                    setSelectedTripScheduleServiceID(null);\n                                    setTripReportSchedules([]);\n                                    setShowNextTrips(false);\n                                }\n                            },\n                            placeholder: \"Select Trip Schedule Service\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1476,\n                            columnNumber: 21\n                        }, this),\n                        selectedTripScheduleServiceID && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_20__.CheckFieldLabel, {\n                            id: \"show-next-trips\",\n                            type: \"checkbox\",\n                            checked: showNextTrips,\n                            onCheckedChange: (checked)=>{\n                                setShowNextTrips(checked);\n                                loadTripReportSchedules(selectedTripScheduleServiceID);\n                            },\n                            label: \"Show next trips\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1499,\n                            columnNumber: 25\n                        }, this),\n                        tripReportSchedules.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table_wrapper__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                            headings: [\n                                \"Depart Time\",\n                                \"Depart Location\",\n                                \"Arrival Time\",\n                                \"Destination\",\n                                \"\"\n                            ],\n                            showHeader: true,\n                            children: tripReportSchedules.map((trs)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: trs.departTime\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1525,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: trs.fromLocation.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1526,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: trs.arriveTime\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1527,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: trs.toLocation.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1528,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                                onClick: ()=>handleSelectTripReportSchedule(trs),\n                                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"]\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1530,\n                                                columnNumber: 41\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1529,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, trs.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 1524,\n                                    columnNumber: 33\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1514,\n                            columnNumber: 25\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-4\",\n                            children: readTripReportSchedulesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 1544,\n                                columnNumber: 33\n                            }, this) : \"Please select a schedule from the dropdown or create a custom trip.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1542,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1469,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1461,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TripLog, \"fcxN08Zon4WnxG1OD71DL34ZJiI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast,\n        nuqs__WEBPACK_IMPORTED_MODULE_40__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_41__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_42__.useMutation\n    ];\n});\n_c = TripLog;\nvar _c;\n$RefreshReg$(_c, \"TripLog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx\n"));

/***/ })

});